.modal-body .modalTitle{
    font-size: 18px;
    font-weight: 500;
    border-bottom: 1px dashed grey;
    padding-bottom: 8px;
    margin: auto auto 15px auto;
}
.custom-inv-table table th, .custom-inv-table table td{
    font-size:14px;
}
/* -- Rich text editor start -- */

/* -- Rich text editor end -- */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker {
  font-family: inherit;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.react-datepicker__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.react-datepicker__day--selected {
  background-color: #007bff;
  border-radius: 4px;
}

.react-datepicker__day:hover {
  background-color: #e9ecef;
}

.react-datepicker__day--keyboard-selected {
  background-color: #007bff;
  color: white;
}
.react-datepicker__input-container input {
  cursor: pointer;
}

.react-datepicker__month-dropdown,
.react-datepicker__year-dropdown {
  max-height: 200px;
  overflow-y: auto;
  font-size: 14px;
  scrollbar-width: thin;
}

.react-datepicker__year-dropdown::-webkit-scrollbar,
.react-datepicker__month-dropdown::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

.react-datepicker__year-dropdown::-webkit-scrollbar-thumb,
.react-datepicker__month-dropdown::-webkit-scrollbar-thumb {
  background-color: #aaa;
  border-radius: 6px;
}

.info-setup{
  border: 1px solid #b1c9db;
  background: rgba(220, 233, 244, 0.50);
  padding: 12px 10px;
  justify-content: space-between;
}