import React, { useState, useEffect, forwardRef } from 'react';
import axios from 'axios';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import RichTextEditor from './RichTextEditorTiny';

// Custom read-only input for DatePicker
const ReadOnlyDateInput = forwardRef(({ value, onClick, onBlur }, ref) => (
  <input
    className="form-control"
    onClick={onClick}
    value={value}
    onBlur={onBlur}
    readOnly
    ref={ref}
    placeholder="MM/DD/YYYY"
    style={{ cursor: 'pointer', backgroundColor: '#fff' }}
  />
));

const EmailReviewPane = ({
  sendEmail, setSendEmail,
  emailTo, setEmailTo,
  cc, setCc,
  bcc, setBcc,
  subject, setSubject,
  emailBody, setEmailBody
}) => (
  <>
    <h5 className="section-title">Email Review Pane</h5>

    <div className="form-check d-flex align-items-center mb-3">
      <input
        className="form-check-input me-2"
        type="checkbox"
        checked={sendEmail}
        onChange={(e) => setSendEmail(e.target.checked)}
      />
      <label className="form-check-label">Send Email Update to Client</label>
    </div>

    <div className="row">
      <div className="col-md-4">
        <label>To:</label>
        <input type="text" className="form-control" value={emailTo} onChange={e => setEmailTo(e.target.value)} />
      </div>
      <div className="col-md-4">
        <label>CC:</label>
        <input type="text" className="form-control" value={cc} onChange={e => setCc(e.target.value)} />
      </div>
      <div className="col-md-4">
        <label>Bcc:</label>
        <input type="text" className="form-control" value={bcc} onChange={e => setBcc(e.target.value)} />
      </div>
    </div>

    <div className="form-group mt-3">
      <label>Subject:*</label>
      <input type="text" className="form-control" value={subject} onChange={e => setSubject(e.target.value)} />
    </div>

    <div className="form-group mt-3">
      <label>Email Body</label>
      <RichTextEditor value={emailBody} onChange={setEmailBody} />
      <div className="text-muted mt-1">Use up to 3000 characters</div>
    </div>
  </>
);

// Paid Modal Content
export const PaidModalContent = ({ modalData }) => {
  const [paymentMode, setPaymentMode] = useState("");
  const [note, setNote] = useState("");
  const [paymentDate, setPaymentDate] = useState(null);
  const [clearedDate, setClearedDate] = useState(null);

  const [sendEmail, setSendEmail] = useState(true);
  const [emailTo, setEmailTo] = useState("");
  const [cc, setCc] = useState("");
  const [bcc, setBcc] = useState("");
  const [subject, setSubject] = useState("");
  const [emailBody, setEmailBody] = useState("<p></p>");
  const [loading, setLoading] = useState(true);

  const paymentModes = [
    { label: "Occams Initiated - eCheck", value: "occams_initiated_eCheck" },
    { label: "Occams Initiated - ACH", value: "occams_initiated_ach" },
    { label: "Client Initiated - Wire", value: "occams_initiated_wire" },
    { label: "Client Initiated - ACH", value: "client_initiated_ach" },
    { label: "Client Initiated - Check Mailed", value: "client_initiated_check_mailed" },
    { label: "Credit Card or Debit Card", value: "credit_card_or_debit_card" },
  ];

  useEffect(() => {
    const invoiceid = modalData?.invoiceId;
    const invoiceAmount = modalData?.invoiceAmount;
    if (!invoiceid || !invoiceAmount) {
      //console.warn("[PaidModal] Required modalData missing:", modalData);
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      const payload = {
        action_type: "2",
        invoiceid: String(invoiceid),
        invoiceAmount: String(invoiceAmount),
      };

      try {
        const response = await axios.post(
          "https://play.occamsadvisory.com/portal/wp-json/invoices/v1/get-invoice-action",
          payload
        );
        const data = response.data || {};
        setEmailTo(data.user_email || "");
        setCc(data.cc_email || "");
        setBcc(data.bcc_email || "");
        setSubject(data.subject || "");
        setEmailBody(data.main_content || "<p></p>");
      } catch (error) {
       // console.error("[PaidModal] API error:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [modalData?.invoiceId, modalData?.invoiceAmount]);

  if (loading) {
    return (
      <div className="modal-body text-center py-5">
        <div className="spinner-border text-primary" role="status"></div>
        <p className="mt-3">Loading invoice data...</p>
      </div>
    );
  }

  return (
    <div className="modal-body">
      <h5 className="section-title">User Input Section</h5>

      <div className="form-group">
        <label>Payment mode/invoice payment type*</label>
        <select
          className="form-select"
          value={paymentMode}
          onChange={(e) => setPaymentMode(e.target.value)}
        >
          <option value="">Select payment mode</option>
          {paymentModes.map((mode) => (
            <option key={mode.value} value={mode.value}>
              {mode.label}
            </option>
          ))}
        </select>
      </div>

      <div className="form-group mt-3">
        <label>Note:</label>
        <input
          type="text"
          className="form-control"
          value={note}
          onChange={(e) => setNote(e.target.value)}
        />
      </div>

      <div className="row mt-3">
        <div className="col-md-6">
          <label>Payment Date*</label>
          <DatePicker
            selected={paymentDate}
            onChange={(date) => setPaymentDate(date)}
            dateFormat="MM/dd/yyyy"
            showMonthDropdown
            showYearDropdown
            dropdownMode="scroll"
            minDate={new Date("1900-01-01")}
            customInput={<ReadOnlyDateInput />}
            placeholderText="MM/DD/YYYY"
          />
        </div>
        <div className="col-md-6">
          <label>Payment Cleared Date*</label>
          <DatePicker
            selected={clearedDate}
            onChange={(date) => setClearedDate(date)}
            dateFormat="MM/dd/yyyy"
            showMonthDropdown
            showYearDropdown
            dropdownMode="scroll"
            minDate={new Date("1900-01-01")}
            customInput={<ReadOnlyDateInput />}
            placeholderText="MM/DD/YYYY"
          />
        </div>
      </div>

      <hr className="mt-4" />
      <EmailReviewPane
        sendEmail={sendEmail}
        setSendEmail={setSendEmail}
        emailTo={emailTo}
        setEmailTo={setEmailTo}
        cc={cc}
        setCc={setCc}
        bcc={bcc}
        setBcc={setBcc}
        subject={subject}
        setSubject={setSubject}
        emailBody={emailBody}
        setEmailBody={setEmailBody}
      />

      <div className="d-flex justify-content-center gap-3 mt-4">
        <button className="btn save-btn">Invoice Paid</button>
        <button className="btn cancel-btn">Cancel</button>
      </div>
    </div>
  );
};

// Cancelled Modal Content (Updated with API Integration and Rich Editor)
export const CancelledModalContent = ({ modalData }) => {
  const [sendEmail, setSendEmail] = useState(true);
  const [emailTo, setEmailTo] = useState("");
  const [cc, setCc] = useState("");
  const [bcc, setBcc] = useState("");
  const [subject, setSubject] = useState("");
  const [emailBody, setEmailBody] = useState("<p></p>");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const invoiceid = modalData?.invoiceId;
    const invoiceAmount = modalData?.invoiceAmount;
    if (!invoiceid || !invoiceAmount) {
      //console.warn("[CancelledModal] Required modalData missing:", modalData);
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      const payload = {
        action_type: "3",
        invoiceid: String(invoiceid),
        invoiceAmount: String(invoiceAmount),
      };

      try {
        const response = await axios.post(
          "https://play.occamsadvisory.com/portal/wp-json/invoices/v1/get-invoice-action",
          payload
        );
        const data = response.data || {};
        setEmailTo(data.user_email || "");
        setCc(data.cc_email || "");
        setBcc(data.bcc_email || "");
        setSubject(data.subject || "");
        setEmailBody(data.main_content || "<p></p>");
      } catch (error) {
        //console.error("[CancelledModal] API error:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [modalData?.invoiceId, modalData?.invoiceAmount]);

  if (loading) {
    return (
      <div className="modal-body text-center py-5">
        <div className="spinner-border text-primary" role="status"></div>
        <p className="mt-3">Loading invoice data...</p>
      </div>
    );
  }

  return (
    <div className="modal-body">
      <EmailReviewPane
        sendEmail={sendEmail}
        setSendEmail={setSendEmail}
        emailTo={emailTo}
        setEmailTo={setEmailTo}
        cc={cc}
        setCc={setCc}
        bcc={bcc}
        setBcc={setBcc}
        subject={subject}
        setSubject={setSubject}
        emailBody={emailBody}
        setEmailBody={setEmailBody}
      />
      <div className="d-flex justify-content-center gap-3 mt-4">
        <button className="btn save-btn">Cancel Invoice</button>
        <button className="btn cancel-btn">Cancel</button>
      </div>
    </div>
  );
};

// Payment in Process Modal Content
export const PaymentProcessModalContent = ({ modalData }) => {
  const [sendEmail, setSendEmail] = useState(true);
  const [emailTo, setEmailTo] = useState("");
  const [cc, setCc] = useState("");
  const [bcc, setBcc] = useState("");
  const [subject, setSubject] = useState("");
  const [emailBody, setEmailBody] = useState("<p></p>");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const invoiceid = modalData?.invoiceId;
    const invoiceAmount = modalData?.invoiceAmount;
    if (!invoiceid || !invoiceAmount) {
      console.warn("[PaymentProcessModal] Required modalData missing:", modalData);
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      const payload = {
        action_type: "6",
        invoiceid: String(invoiceid),
        invoiceAmount: String(invoiceAmount),
      };

      try {
        const response = await axios.post(
          "https://play.occamsadvisory.com/portal/wp-json/invoices/v1/get-invoice-action",
          payload
        );
        const data = response.data || {};
        setEmailTo(data.user_email || "");
        setCc(data.cc_email || "");
        setBcc(data.bcc_email || "");
        setSubject(data.subject || "");
        setEmailBody(data.main_content || "<p></p>");
      } catch (error) {
        console.error("[PaymentProcessModal] API error:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [modalData?.invoiceId, modalData?.invoiceAmount]);

  if (loading) {
    return (
      <div className="modal-body text-center py-5">
        <div className="spinner-border text-primary" role="status"></div>
        <p className="mt-3">Loading invoice data...</p>
      </div>
    );
  }

  return (
    <div className="modal-body">
      <EmailReviewPane
        sendEmail={sendEmail}
        setSendEmail={setSendEmail}
        emailTo={emailTo}
        setEmailTo={setEmailTo}
        cc={cc}
        setCc={setCc}
        bcc={bcc}
        setBcc={setBcc}
        subject={subject}
        setSubject={setSubject}
        emailBody={emailBody}
        setEmailBody={setEmailBody}
      />
      <div className="d-flex justify-content-center gap-3 mt-4">
        <button className="btn save-btn">Payment in Process</button>
        <button className="btn cancel-btn">Cancel</button>
      </div>
    </div>
  );
};

// Partial Paid Modal Content
export const PartialPaidModalContent = ({ modalData }) => {
  const [rows, setRows] = useState([]);
  const [inputRows, setInputRows] = useState([
    {
      refId: "",
      paymentDate: new Date(),
      clearedDate: new Date(),
      paymentMode: "",
      note: "",
      service: "",
      charge: "",
      received: "",
    },
  ]);
  const [sendEmail, setSendEmail] = useState(true);
  const [emailBody, setEmailBody] = useState("<p></p>");
  const [emailTo, setEmailTo] = useState("");
  const [cc, setCc] = useState("");
  const [bcc, setBcc] = useState("");
  const [subject, setSubject] = useState("");
  const [loading, setLoading] = useState(true);
  const [overdueAmount, setOverdueAmount] = useState("0.00");
  const [receivedAmount, setReceivedAmount] = useState("0.00");

  const parseDollar = (str) => parseFloat(String(str).replace(/[^0-9.]/g, "") || "0") || 0;
  const safeDate = (str) => {
    const d = new Date(str);
    return isNaN(d.getTime()) ? new Date() : d;
  };
  const formatDateToDMY = (date) => {
    if (!date || isNaN(new Date(date).getTime())) return "NA";
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${month}/${day}/${year}`;
  };
  const paymentModes = [
    { label: "Occams Initiated - eCheck", value: "occams_initiated_eCheck" },
    { label: "Occams Initiated - ACH", value: "occams_initiated_ach" },
    { label: "Client Initiated - Wire", value: "occams_initiated_wire" },
    { label: "Client Initiated - ACH", value: "client_initiated_ach" },
    { label: "Client Initiated - Check Mailed", value: "client_initiated_check_mailed" },
    { label: "Credit Card or Debit Card", value: "credit_card_or_debit_card" },
  ];
  const getPaymentModeLabel = (value) => {
    const match = paymentModes.find(pm => pm.value === value);
    return match ? match.label : value || "NA";
  };

  const getPaymentModeValue = (label) => {
    const match = paymentModes.find(pm => pm.label === label);
    return match ? match.value : label || "";
  };

  useEffect(() => {
    const invoiceid = modalData?.invoiceId;
    const invoiceAmount = modalData?.invoiceAmount;
    if (!invoiceid || !invoiceAmount) {
      //console.warn("[PartialPaidModal] Required modalData missing:", modalData);
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      //console.log("[PartialPaidModal] modalData received:", modalData);
      const payload = {
        action_type: "17",
        invoiceid: String(invoiceid),
        invoiceAmount: String(invoiceAmount),
      };

      try {
        const response = await axios.post(
          "https://play.occamsadvisory.com/portal/wp-json/invoices/v1/get-invoice-action",
          payload
        );
        const data = response.data || {};
        //console.log("[PartialPaidModalSSDATA] Raw API response:", data);
        setEmailTo(data.user_email || "");
        setCc(data.cc_email || "");
        setBcc(data.bcc_email || "");
        setSubject(data.subject || "");
        setEmailBody(data.main_content || "<p></p>");

        setOverdueAmount(data.overdue_amount || "0.00");
        setReceivedAmount(data.recieved_amount || "0.00");

        const parser = new DOMParser();
        const htmlDoc = parser.parseFromString(
          data.payment_table_html || "",
          "text/html"
        );
        const tableRows = [...htmlDoc.querySelectorAll("tr")].filter((row) =>
          row.className.includes("ppamt")
        );

        const parsedRows = tableRows.map((row) => {
          const cells = row.querySelectorAll("td");
          return {
            refId: cells[0]?.textContent?.trim() || "",
            paymentDate: safeDate(cells[1]?.textContent?.trim()),
            clearedDate: safeDate(cells[2]?.textContent?.trim()),
            paymentMode: getPaymentModeValue(cells[3]?.textContent?.trim()),
            note: cells[4]?.textContent?.trim() || "",
            service: parseDollar(cells[5]?.textContent),
            charge: parseDollar(cells[6]?.textContent),
            received: parseDollar(cells[7]?.textContent),
          };
        });

        setRows(parsedRows);
        if (parsedRows.length > 0) {
          setInputRows(parsedRows);
        }
      } catch (error) {
        //console.error("[PartialPaidModal] API error:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [modalData?.invoiceId, modalData?.invoiceAmount]);
  const handleInputChange = (index, field, value) => {
    const updated = [...inputRows];
    updated[index][field] = value;
    setInputRows(updated);
  };

  const handleInputAdd = () => {
    setInputRows([
      ...inputRows,
      {
        refId: "",
        paymentDate: new Date(),
        clearedDate: new Date(),
        paymentMode: "",
        note: "",
        service: "",
        charge: "",
        received: "",
      },
    ]);
  };

  const handleInputRemove = (index) => {
    setInputRows(inputRows.filter((_, i) => i !== index));
  };

  if (loading) {
    return (
      <div className="modal-body text-center py-5">
        <div className="spinner-border text-primary" role="status"></div>
        <p className="mt-3">Loading invoice data...</p>
      </div>
    );
  }

  return (
    <div className="modal-body">
      <h5 className="section-title">User Input Section</h5>

      <div className="table-responsive custom-inv-table">
        <table className="table table-bordered table-sm mb-0">
          <thead className="bg-light">
            <tr>
              <th>Reference ID*</th>
              <th>Payment Date*</th>
              <th>Payment Cleared Date*</th>
              <th>Payment Mode*</th>
              <th>Note</th>
              <th>Payment - Service</th>
              <th>Payment - Charge</th>
              <th>Payment Received*</th>
            </tr>
          </thead>
          <tbody>
            {rows.length > 0 ? (
              rows.map((row, index) => (
                <tr key={index}>
                  <td>{row.refId || "NA"}</td>
                  <td>{formatDateToDMY(row.paymentDate)}</td>
                  <td>{formatDateToDMY(row.clearedDate)}</td>
                  <td>{getPaymentModeLabel(row.paymentMode)}</td>
                  <td>{row.note || "NA"}</td>
                  <td>{row.service !== "" ? `$${parseFloat(row.service).toFixed(2)}` : "NA"}</td>
                  <td>{row.charge !== "" ? `$${parseFloat(row.charge).toFixed(2)}` : "NA"}</td>
                  <td>{row.received !== "" ? `$${parseFloat(row.received).toFixed(2)}` : "NA"}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="8" className="text-center text-muted py-3">
                  No payments found for this invoice.
                </td>
              </tr>
            )}
          </tbody>
        </table>

        <table className="table table-bordered table-sm mt-3">
          <tbody>
            {inputRows.map((row, index) => (
              <tr key={`input-${index}`}>
                <td>
                  <input
                    type="text"
                    className="form-control"
                    value={row.refId}
                    onChange={(e) =>
                      handleInputChange(index, "refId", e.target.value)
                    }
                  />
                </td>
                <td>
                  <DatePicker
                    selected={row.paymentDate}
                    onChange={(date) =>
                      handleInputChange(index, "paymentDate", date)
                    }
                    dateFormat="MM/dd/yyyy"
                    customInput={<ReadOnlyDateInput />}
                  />
                </td>
                <td>
                  <DatePicker
                    selected={row.clearedDate}
                    onChange={(date) =>
                      handleInputChange(index, "clearedDate", date)
                    }
                    dateFormat="MM/dd/yyyy"
                    customInput={<ReadOnlyDateInput />}
                  />
                </td>
                <td>
                  <select
                    className="form-select"
                    value={row.paymentMode}
                    onChange={(e) =>
                      handleInputChange(index, "paymentMode", e.target.value)
                    }
                  >
                    <option value="">Select payment mode</option>
                    {paymentModes.map((mode) => (
                      <option key={mode.value} value={mode.value}>
                        {mode.label}
                      </option>
                    ))}
                  </select>
                </td>
                <td>
                  <input
                    type="text"
                    className="form-control"
                    value={row.note}
                    onChange={(e) =>
                      handleInputChange(index, "note", e.target.value)
                    }
                  />
                </td>
                <td>
                  <input
                    type="number"
                    className="form-control"
                    value={row.service}
                    onChange={(e) =>
                      handleInputChange(index, "service", e.target.value)
                    }
                  />
                </td>
                <td>
                  <input
                    type="number"
                    className="form-control"
                    value={row.charge}
                    onChange={(e) =>
                      handleInputChange(index, "charge", e.target.value)
                    }
                  />
                </td>
                <td className="d-flex align-items-center">
                  <input
                    type="number"
                    className="form-control me-2"
                    value={row.received}
                    onChange={(e) =>
                      handleInputChange(index, "received", e.target.value)
                    }
                  />
                  {inputRows.length > 1 && (
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => handleInputRemove(index)}
                    >
                      &#10006;
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="d-flex justify-content-end mb-3">
        <a
          href="javascript:void(0);"
          className="px-3 py-2 column-selector-btn"
          onClick={handleInputAdd}
        >
          <i className="fa fa-plus-circle" aria-hidden="true"></i> Add Payment
        </a>
      </div>
      
      <div className="rounded info-setup mb-3">
        <div className="d-flex justify-content-between">
          <div>
            <strong>Total Partial Payment Amount</strong>
          </div>
          <div>${receivedAmount}</div>
        </div>
      </div>
      
      <div className="rounded info-setup mb-3">
        <div className="d-flex justify-content-between">
          <div>
            <strong>Overdue Amount</strong>
            <i className="bi bi-info-circle-fill text-primary"></i>
          </div>
          <div>${overdueAmount}</div>
        </div>
      </div>

      <EmailReviewPane
        sendEmail={sendEmail}
        setSendEmail={setSendEmail}
        emailTo={emailTo}
        setEmailTo={setEmailTo}
        cc={cc}
        setCc={setCc}
        bcc={bcc}
        setBcc={setBcc}
        subject={subject}
        setSubject={setSubject}
        emailBody={emailBody}
        setEmailBody={setEmailBody}
      />

      <div className="d-flex justify-content-center gap-3 mt-4">
        <button className="btn save-btn">Partially paid</button>
        <button className="btn cancel-btn">Cancel</button>
      </div>
    </div>
  );
};

// Resend invoice
export const ResendInvoiceModalContent = ({ modalData }) => {
  const [sendEmail, setSendEmail] = useState(true);
  const [emailTo, setEmailTo] = useState("");
  const [cc, setCc] = useState("");
  const [bcc, setBcc] = useState("");
  const [subject, setSubject] = useState("");
  const [emailBody, setEmailBody] = useState("<p></p>");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const invoiceid = modalData?.invoiceId;
    const invoiceAmount = modalData?.invoiceAmount;
    if (!invoiceid || !invoiceAmount) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      const payload = {
        action_type: "resend",
        invoiceid: String(invoiceid),
        invoiceAmount: String(invoiceAmount),
      };

      try {
        const response = await axios.post(
          "https://play.occamsadvisory.com/portal/wp-json/invoices/v1/get-invoice-action",
          payload
        );
        const data = response.data || {};
        setEmailTo(data.user_email || "");
        setCc(data.cc_email || "");
        setBcc(data.bcc_email || "");
        setSubject(data.subject || "");
        setEmailBody(data.main_content || "<p></p>");
      } catch (error) {
        console.error("[ResendInvoiceModal] API error:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [modalData?.invoiceId, modalData?.invoiceAmount]);

  if (loading) {
    return (
      <div className="modal-body text-center py-5">
        <div className="spinner-border text-primary" role="status"></div>
        <p className="mt-3">Loading invoice data...</p>
      </div>
    );
  }

  return (
    <div className="modal-body">
      <EmailReviewPane
        sendEmail={sendEmail}
        setSendEmail={setSendEmail}
        emailTo={emailTo}
        setEmailTo={setEmailTo}
        cc={cc}
        setCc={setCc}
        bcc={bcc}
        setBcc={setBcc}
        subject={subject}
        setSubject={setSubject}
        emailBody={emailBody}
        setEmailBody={setEmailBody}
      />

      <div className="d-flex justify-content-center gap-3 mt-4">
        <button className="btn save-btn">Resend Invoice</button>
        <button className="btn cancel-btn">Cancel</button>
      </div>
    </div>
  );
};
// Reminder Modal Content
export const ReminderModalContent = ({ modalData }) => (
  <div className="modal-body">
    <h5 className="section-title">Send Payment Reminder</h5>
    {/* <InvoiceDetails {...modalData} /> */}
    <div className="form-group">
      <label>Reminder Type</label>
      <select className="form-select">
        <option value="">Select Reminder Type</option>
        <option value="friendly">Friendly Reminder</option>
        <option value="urgent">Urgent Payment Required</option>
        <option value="final">Final Notice</option>
      </select>
    </div>
    <div className="form-group mt-3">
      <label>Custom Message</label>
      <textarea className="form-control" rows="3" placeholder="Enter custom message (optional)"></textarea>
    </div>
  </div>
);

// Payment Plan Modal Content
export const PaymentPlanModalContent = ({ modalData }) => (
  <div className="modal-body">
    <h5 className="section-title">Create Payment Plan</h5>
    {/* <InvoiceDetails {...modalData} /> */}
    <div className="form-group">
      <label>Number of Installments</label>
      <select className="form-select">
        <option value="2">2 Installments</option>
        <option value="3">3 Installments</option>
        <option value="4">4 Installments</option>
        <option value="6">6 Installments</option>
      </select>
    </div>
    <div className="form-group mt-3">
      <label>Payment Frequency</label>
      <select className="form-select">
        <option value="weekly">Weekly</option>
        <option value="biweekly">Bi-weekly</option>
        <option value="monthly">Monthly</option>
      </select>
    </div>
  </div>
);

// Pause Invoice Reminder Modal Content
export const PauseReminderModalContent = ({ modalData }) => (
  <div className="modal-body">
    <h5 className="section-title">Pause Invoice Reminder</h5>
    <div className="confirmation-message mt-3">
      <p className="text-muted">
        Are you sure you want to pause automatic reminders for this invoice?
      </p>
    </div>
  </div>
);

// Default Modal Content
export const DefaultModalContent = ({ modalData, actionText }) => (
  <div className="modal-body">
    <h5 className="section-title">{actionText}</h5>
    {/* <InvoiceDetails {...modalData} /> */}
    <div className="confirmation-message mt-3">
      <p className="text-muted">
        Are you sure you want to {actionText.toLowerCase()} this invoice?
      </p>
    </div>
  </div>
);

// Main Modal Content Component
const ModalContent = ({ modalData, actionsMap }) => {
  const { actionType } = modalData;
  const actionText = actionsMap[actionType]?.text || actionType;
  //const sizeClass = modalData.actionType === '17' ? 'modal-xl' : 'modal-md';

  switch (actionType) {
    case '2':
      return <PaidModalContent modalData={modalData} />;
    case '3':
      return <CancelledModalContent modalData={modalData} />;
    case '4':
      return <DraftModalContent modalData={modalData} />;
    case '5':
      return <ReminderModalContent modalData={modalData} />;
    case '6':
      return <PaymentProcessModalContent modalData={modalData} />;
    case '17':
      return <PartialPaidModalContent modalData={modalData} />;
    case '19':
      return <PaymentPlanModalContent modalData={modalData} />;
    case 'resend':
      return <ResendInvoiceModalContent modalData={modalData} />;  
    case 'cancel_auto_inv_reminder':
      return <PauseReminderModalContent modalData={modalData} />;
    default:
      return <DefaultModalContent modalData={modalData} actionText={actionText} />;
  }
};

export default ModalContent; 